-- Fix for missing chat table in globalne_itunnel database
-- This script creates the missing chat table that is referenced in includes/functions.php

USE globalne_itunnel;

-- Create the chat table
CREATE TABLE IF NOT EXISTS `chat` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chat_id1` int(11) NOT NULL,
  `chat_id2` int(11) NOT NULL,
  `chat_message` text NOT NULL,
  `chat_status` enum('sent','seen','unread') NOT NULL DEFAULT 'unread',
  `chat_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `chat_type` enum('user','admin','system') NOT NULL DEFAULT 'user',
  PRIMARY KEY (`id`),
  KEY `idx_chat_id1` (`chat_id1`),
  KEY `idx_chat_id2` (`chat_id2`),
  KEY `idx_chat_status` (`chat_status`),
  KEY `idx_chat_date` (`chat_date`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;

-- Insert some sample data (optional - you can remove this if not needed)
-- INSERT INTO `chat` (`chat_id1`, `chat_id2`, `chat_message`, `chat_status`, `chat_type`) VALUES
-- (1, 2, 'Welcome to the chat system!', 'seen', 'system'),
-- (2, 1, 'Thank you for the welcome!', 'unread', 'user');

-- Verify the table was created
SHOW TABLES LIKE 'chat';
DESCRIBE chat;
