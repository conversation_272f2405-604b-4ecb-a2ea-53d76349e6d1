<?php
// Test SSH2 extension functionality
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>SSH2 Extension Test</h1>";

// Check if SSH2 extension is loaded
if (extension_loaded('ssh2')) {
    echo "<p style='color: green;'>✓ SSH2 extension is loaded</p>";
} else {
    echo "<p style='color: red;'>✗ SSH2 extension is NOT loaded</p>";
    exit;
}

// Check if SSH2 functions are available
$functions = ['ssh2_connect', 'ssh2_auth_password', 'ssh2_exec', 'ssh2_fetch_stream'];
foreach ($functions as $func) {
    if (function_exists($func)) {
        echo "<p style='color: green;'>✓ Function $func is available</p>";
    } else {
        echo "<p style='color: red;'>✗ Function $func is NOT available</p>";
    }
}

echo "<h2>SSH2 Extension Information</h2>";
echo "<pre>";
$reflection = new ReflectionExtension('ssh2');
echo "Version: " . $reflection->getVersion() . "\n";
echo "Functions: " . count($reflection->getFunctions()) . "\n";
echo "</pre>";

echo "<p><strong>SSH2 extension is working correctly!</strong></p>";
?>
