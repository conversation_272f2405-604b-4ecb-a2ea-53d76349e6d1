<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title>Responsive Design Test - RajaGenWeb</title>
    <link rel="stylesheet" href="/dist/modules/bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="/dist/css-orange/style.css">
    <link rel="stylesheet" href="/dist/css-orange/components.css">
    <style>
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        .screen-size-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #007bff;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
        }
        @media (max-width: 575.98px) {
            .screen-size-indicator::after { content: "XS (< 576px)"; }
        }
        @media (min-width: 576px) and (max-width: 767.98px) {
            .screen-size-indicator::after { content: "SM (576px - 767px)"; }
        }
        @media (min-width: 768px) and (max-width: 991.98px) {
            .screen-size-indicator::after { content: "MD (768px - 991px)"; }
        }
        @media (min-width: 992px) and (max-width: 1199.98px) {
            .screen-size-indicator::after { content: "LG (992px - 1199px)"; }
        }
        @media (min-width: 1200px) {
            .screen-size-indicator::after { content: "XL (≥ 1200px)"; }
        }
    </style>
</head>
<body>
    <div class="screen-size-indicator"></div>
    
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mt-4 mb-4">
                    <i class="fas fa-mobile-alt"></i> Responsive Design Test
                </h1>
            </div>
        </div>
        
        <!-- Bootstrap Grid Test -->
        <div class="test-section">
            <h3><i class="fas fa-th"></i> Bootstrap Grid System</h3>
            <div class="row">
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <div class="card mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Column 1</h5>
                            <p class="card-text">12/6/4/3 columns</p>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <div class="card mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Column 2</h5>
                            <p class="card-text">12/6/4/3 columns</p>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <div class="card mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Column 3</h5>
                            <p class="card-text">12/6/4/3 columns</p>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <div class="card mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Column 4</h5>
                            <p class="card-text">12/6/4/3 columns</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Form Elements Test -->
        <div class="test-section">
            <h3><i class="fas fa-edit"></i> Form Elements</h3>
            <form>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="username">Username</label>
                            <input type="text" class="form-control" id="username" placeholder="Enter username">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="email" class="form-control" id="email" placeholder="Enter email">
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="message">Message</label>
                    <textarea class="form-control" id="message" rows="3" placeholder="Enter your message"></textarea>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-paper-plane"></i> Submit
                </button>
            </form>
        </div>
        
        <!-- Navigation Test -->
        <div class="test-section">
            <h3><i class="fas fa-bars"></i> Navigation</h3>
            <nav class="navbar navbar-expand-lg navbar-light bg-light">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-home"></i> Brand
                </a>
                <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav">
                        <li class="nav-item active">
                            <a class="nav-link" href="#">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">Features</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">About</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">Contact</a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
        
        <!-- Button Test -->
        <div class="test-section">
            <h3><i class="fas fa-mouse-pointer"></i> Buttons & Components</h3>
            <div class="row">
                <div class="col-12 col-sm-6 col-lg-3 mb-2">
                    <button class="btn btn-primary btn-block">
                        <i class="fas fa-check"></i> Primary
                    </button>
                </div>
                <div class="col-12 col-sm-6 col-lg-3 mb-2">
                    <button class="btn btn-success btn-block">
                        <i class="fas fa-thumbs-up"></i> Success
                    </button>
                </div>
                <div class="col-12 col-sm-6 col-lg-3 mb-2">
                    <button class="btn btn-warning btn-block">
                        <i class="fas fa-exclamation-triangle"></i> Warning
                    </button>
                </div>
                <div class="col-12 col-sm-6 col-lg-3 mb-2">
                    <button class="btn btn-danger btn-block">
                        <i class="fas fa-times"></i> Danger
                    </button>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4 mb-4">
            <a href="/" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Main Site
            </a>
        </div>
    </div>
    
    <script src="/dist/modules/jquery.min.js"></script>
    <script src="/dist/modules/bootstrap/js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            console.log('Responsive test page loaded successfully!');
            console.log('Current viewport width:', $(window).width());
            
            $(window).resize(function() {
                console.log('Viewport resized to:', $(window).width());
            });
        });
    </script>
</body>
</html>
