<?php
// Test manual extension loading
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Manual SSH2 Extension Loading Test</h1>";

echo "<h2>Before Loading</h2>";
echo "extension_loaded('ssh2'): " . (extension_loaded('ssh2') ? 'YES' : 'NO') . "<br>";

echo "<h2>Attempting to load extension manually</h2>";
$ext_dir = ini_get('extension_dir');
$ssh2_path = $ext_dir . DIRECTORY_SEPARATOR . 'php_ssh2.dll';

echo "Extension path: " . $ssh2_path . "<br>";
echo "File exists: " . (file_exists($ssh2_path) ? 'YES' : 'NO') . "<br>";

if (function_exists('dl')) {
    echo "dl() function available: YES<br>";
    try {
        $result = dl('php_ssh2.dll');
        echo "dl() result: " . ($result ? 'SUCCESS' : 'FAILED') . "<br>";
    } catch (Exception $e) {
        echo "dl() exception: " . $e->getMessage() . "<br>";
    }
} else {
    echo "dl() function available: NO (disabled in this PHP configuration)<br>";
}

echo "<h2>After Loading Attempt</h2>";
echo "extension_loaded('ssh2'): " . (extension_loaded('ssh2') ? 'YES' : 'NO') . "<br>";
echo "function_exists('ssh2_connect'): " . (function_exists('ssh2_connect') ? 'YES' : 'NO') . "<br>";
?>
