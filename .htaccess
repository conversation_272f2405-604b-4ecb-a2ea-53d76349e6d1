RewriteEngine On
RewriteBase /RajaGenWeb/

# Redirect to HTTPS if needed (uncomment if you want to force HTTPS)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Block direct access to sensitive directories
RewriteRule ^(includes|content|templates|templates_c|uploads)/ - [F,L]

# Main URL rewriting rule - convert /dashboard to /?p=dashboard
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^([^/.]+)/?$ index.php?p=$1 [L,QSA]

# Handle activation links
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^activate/([0-9]+)/([^/.]+)/?$ index.php?p=activate&code=$1&email=$2 [L,QSA]

# Handle support ticket links
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^supportticket/(.*)/([^/.]+)/?$ index.php?p=supportticket&id=$1&user=$2 [L,QSA]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set X-Content-Type-Options nosniff
</IfModule>

# Deny access to sensitive files
<FilesMatch "\.(htaccess|htpasswd|ini|phps|fla|psd|log|sh|sql)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Deny access to config files
<FilesMatch "^(config|db_config)\.php$">
    Order allow,deny
    Deny from all
</FilesMatch>
