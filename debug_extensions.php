<?php
// Debug extension loading
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>PHP Extension Debug</h1>";

echo "<h2>Loaded Extensions</h2>";
$extensions = get_loaded_extensions();
sort($extensions);
echo "<pre>";
foreach ($extensions as $ext) {
    echo $ext . "\n";
}
echo "</pre>";

echo "<h2>SSH2 Extension Check</h2>";
echo "extension_loaded('ssh2'): " . (extension_loaded('ssh2') ? 'YES' : 'NO') . "<br>";
echo "function_exists('ssh2_connect'): " . (function_exists('ssh2_connect') ? 'YES' : 'NO') . "<br>";

echo "<h2>PHP Configuration</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Extension Directory: " . ini_get('extension_dir') . "<br>";
echo "Loaded Configuration File: " . php_ini_loaded_file() . "<br>";

echo "<h2>Extension Directory Contents</h2>";
$ext_dir = ini_get('extension_dir');
if (is_dir($ext_dir)) {
    $files = scandir($ext_dir);
    echo "<pre>";
    foreach ($files as $file) {
        if (strpos($file, 'ssh') !== false) {
            echo "SSH-related: " . $file . "\n";
        }
    }
    echo "</pre>";
}
?>
