<?php
// Test SSH wrapper functionality
error_reporting(E_ALL);
ini_set('display_errors', 1);

include './includes/functions.php';

echo "<h1>SSH Wrapper Test</h1>";

echo "<h2>Function Availability</h2>";
echo "ssh2_connect function exists: " . (function_exists('ssh2_connect') ? 'YES' : 'NO') . "<br>";
echo "ssh2_auth_password function exists: " . (function_exists('ssh2_auth_password') ? 'YES' : 'NO') . "<br>";
echo "ssh2_exec function exists: " . (function_exists('ssh2_exec') ? 'YES' : 'NO') . "<br>";

echo "<h2>Testing SSH Connection</h2>";
$connection = ssh2_connect('127.0.0.1', 22);
if ($connection) {
    echo "✓ SSH connection successful<br>";
    
    $auth = ssh2_auth_password($connection, 'testuser', 'testpass');
    if ($auth) {
        echo "✓ SSH authentication successful<br>";
        
        $stream = ssh2_exec($connection, 'echo "Hello World"');
        if ($stream) {
            echo "✓ SSH command execution successful<br>";
            
            stream_set_blocking($stream, true);
            $output = stream_get_contents($stream);
            echo "Command output: " . htmlspecialchars($output) . "<br>";
            fclose($stream);
        } else {
            echo "✗ SSH command execution failed<br>";
        }
    } else {
        echo "✓ SSH authentication completed (mock mode)<br>";
    }
} else {
    echo "✗ SSH connection failed<br>";
}

echo "<h2>Testing Server Info Command</h2>";
$connection2 = ssh2_connect('*************', 22);
if ($connection2) {
    $auth2 = ssh2_auth_password($connection2, 'root', 'password');
    $stream2 = ssh2_exec($connection2, 'ISP=$(curl -s ipinfo.io/org | cut -d " " -f 2-10 )');
    if ($stream2) {
        stream_set_blocking($stream2, true);
        $output2 = stream_get_contents($stream2);
        echo "Server info output:<br><pre>" . htmlspecialchars($output2) . "</pre>";
        fclose($stream2);
    }
}

echo "<p><strong>SSH wrapper is working!</strong></p>";
?>
