<?php
/* Smarty version 3.1.29, created on 2025-07-13 22:39:40
  from "C:\xampp\htdocs\RajaGenWeb\templates\developer-settings.tpl" */

if ($_smarty_tpl->smarty->ext->_validateCompiled->decodeProperties($_smarty_tpl, array (
  'has_nocache_code' => false,
  'version' => '3.1.29',
  'unifunc' => 'content_68740b7ce9b893_60245806',
  'file_dependency' => 
  array (
    '0e5cba180ad4db98bec9419a1146881982bc831b' => 
    array (
      0 => 'C:\\xampp\\htdocs\\RajaGenWeb\\templates\\developer-settings.tpl',
      1 => 1752434328,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:css/custom_css.tpl' => 1,
    'file:apps/topnav.tpl' => 1,
    'file:apps/sidenav.tpl' => 1,
    'file:js/page/custom_js.tpl' => 1,
    'file:js/page/notification_js.tpl' => 1,
    'file:js/page/search_js.tpl' => 1,
    'file:js/page/developer_js.tpl' => 1,
  ),
),false)) {
function content_68740b7ce9b893_60245806 ($_smarty_tpl) {
?>
<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
<meta charset="UTF-8">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no" name="viewport">
<title><?php echo $_smarty_tpl->tpl_vars['site_name']->value;?>
 — <?php echo $_smarty_tpl->tpl_vars['site_description']->value;?>
</title>
<link rel="shortcut icon" href="<?php echo $_smarty_tpl->tpl_vars['site_logo']->value;?>
" type="image/x-icon">
<link rel="icon" href="<?php echo $_smarty_tpl->tpl_vars['site_logo']->value;?>
" type="image/x-icon">

<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" integrity="sha512-1ycn6IcaQQ40/MKBW2W4Rhis/DbILU74C1vSrLJxCq57o941Ym01SwNsOMqvEBFlcgUa6xLiPY/NS5R+E6ztJQ==" crossorigin="anonymous" referrerpolicy="no-referrer" />

<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/bootstrap-social/bootstrap-social.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/datatables/datatables.min.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/datatables/Select-1.2.4/css/select.bootstrap4.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.9/summernote-bs4.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/select2.min.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/sweetalert2/sweetalert2.min.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/css-<?php echo $_smarty_tpl->tpl_vars['site_theme']->value;?>
/style.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/css-<?php echo $_smarty_tpl->tpl_vars['site_theme']->value;?>
/components.css">
<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:css/custom_css.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

</head>

<body>

<div class="center" id="loading">
    <div class='building-blocks'>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
    </div>
</div>

<div class="main-wrapper">
<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:apps/topnav.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:apps/sidenav.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>


<div class="main-content">
    <section class="section">
        <div class="section-header">
            <h1>Developer Settings</h1>
            <div class="section-header-breadcrumb">
<div class="breadcrumb-item">Developer</div>
<div class="breadcrumb-item active">Settings</div>
</div>
        </div>
        <div class="section-error">
            <div class="errors"></div>
        </div>
        <div class="section-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h2 class="section-title">General</h2>
                        </div>
                        <div class="card-body">
                            <form class="gensettings" accept-charset="UTF-8" autocomplete="off">
                                <input type="hidden" name="_key" id="_key" value="<?php echo $_smarty_tpl->tpl_vars['firenet_encrypt']->value;?>
">
                                <input type="hidden" name="submitted" id="submitted" value="git_settings">
                                <div class="form-group">
                                    <label for="github_username">Github Username</label>
                                    <input id="github_username" type="text" value="" class="form-control github_username" name="github_username" tabindex="1">
                                </div>
                                <div class="form-group">
                                    <label for="github_token">Github Token</label>
                                    <input id="github_token" type="text" value="" class="form-control github_token" name="github_token" tabindex="1">
                                </div>
                                <div class="form-group">
                                    <label for="upnotice">Notice Link</label>
                                    <input id="upnotice" type="text" value="" class="form-control upnotice" name="upnotice" tabindex="1">
                                </div>
                                <div class="form-group">
                                    <label for="license">License Key</label>
                                    <input id="license" type="text" value="" class="form-control license" name="license" tabindex="1">
                                </div>
                                <div class="form-group">
                                    <label for="turnstile">Turnstile Key (Cloudflare)</label>
                                    <input id="turnstile" type="text" value="" class="form-control turnstile" name="turnstile" tabindex="1">
                                </div>
                                <div class="form-group">
                                    <label for="turnstilesecret">Turnstile Secret (Cloudflare)</label>
                                    <input id="turnstilesecret" type="text" value="" class="form-control turnstilesecret" name="turnstilesecret" tabindex="1">
                                </div>
                                <div class="form-group">
                                    <label for="whois">API Layer Key (Whois)</label>
                                    <input id="whois" type="text" value="" class="form-control whois" name="whois" tabindex="1">
                                    <small>Generate API key <a href="https://apilayer.com/marketplace/whois-api" target="_blank">HERE</a></small>
                                </div>
                                <div class="form-group" id="generalsettings">
                                    <button type="button" class="btn btn-primary btn-confirm-web" tabindex="4"> Confirm</button>
                                    <button type="submit" class="btn btn-success btn-confirm-auth d-none" tabindex="4"> Authorize</button>
                                    <button type="button" class="btn btn-confirm-cancel btn-danger d-none" tabindex="4">Cancel</button>
                                </div>
                            </form>
                        </div>
                    </div>
                <div class="card">
                    <div class="card-header">
                        <h2 class="section-title">Cloudflare Dns</h2>
                    </div>
                    <div class="card-body">
                        <form action="" class="dnsupdate" autocomplete="off">
                            <input type="hidden" name="_key" id="_key" value="<?php echo $_smarty_tpl->tpl_vars['firenet_encrypt']->value;?>
">
                            <input type="hidden" name="submitted" id="submitted" value="dns_update">
                            <div class="form-group">
                                <label>Domain</label>
                                <input type="text" placeholder="Enter domain" value="" class="form-control dns_domain" name="dns_domain" tabindex="1">
                            </div>
                            <div class="form-group">
                                <label>Zone ID</label>
                                <input type="text" placeholder="Enter zone id" value="" class="form-control dns_zone" name="dns_zone" tabindex="1">
                            </div>
                            <div class="form-group">
                                <label>Global API</label>
                                <input type="text" placeholder="Enter global api" value="" class="form-control dns_global" name="dns_global" tabindex="1">
                            </div>
                            <div class="form-group">
                                <label>Email Address</label>
                                <input type="text" placeholder="Enter email address" value="" class="form-control dns_email" name="dns_email" tabindex="1">
                            </div>
                            <div class="form-group" id="dnssettings">
                                <button type="button" class="btn btn-primary btn-confirm-dns" tabindex="4"> Confirm</button>
                                <button type="submit" class="btn btn-success btn-confirm-auth d-none" tabindex="4"> Authorize</button>
                                <button type="button" class="btn btn-confirm-cancel btn-danger d-none" tabindex="4">Cancel</button>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="card">
                    <div class="card-header">
                        <h2 class="section-title">Database Backup</h2>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-primary" role="alert">
                            <h6 class="alert-heading"><i class="fas fa-book"></i> Note </h6>
                            <code>Backup Database</code> : Can backup full panel database manually or automatically.<br>
                            <code>Manual Backup</code> : Just click the Backup button.<br>
                            <code>Automatic Backup</code> : You need to add <span class="text-secondary"><u>/includes/cronjob/cronjob_backup.php</u></span> in hosting cronjobs.
                        </div>
                        <form action="" class="dbbak" autocomplete="off">
                            <input type="hidden" name="_key" id="_key" value="<?php echo $_smarty_tpl->tpl_vars['firenet_encrypt']->value;?>
">
                            <input type="hidden" name="submitted" id="submitted" value="database_backup">
                            <div class="form-group">
                                <label>Recipient Email</label>
                                <input type="email" placeholder="Enter recipient email" value="" class="form-control recipient_email" name="recipient_email" tabindex="1">
                            </div>
                            <div class="form-group">
                                <label>CC Email (Optional)</label>
                                <input type="email" placeholder="Enter cc email" value="" class="form-control cc_email" name="cc_email" tabindex="1">
                            </div>
                            <div class="form-group" id="dbbakup">
                                <button type="button" class="btn btn-primary btn-confirm-db" tabindex="4"> Confirm</button>
                                <button type="submit" class="btn btn-success btn-confirm-auth d-none" tabindex="4"> Authorize</button>
                                <button type="button" class="btn btn-confirm-cancel btn-danger d-none" tabindex="4">Cancel</button>
                                <button type="button" class="btn btn-warning btn-confirm-manbak" onclick="manual_bak()" tabindex="4"> Backup</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                        <h2 class="section-title">Administrator</h2>
                    </div>
                <div class="card-body">
                    <form class="adminsettings" accept-charset="UTF-8" autocomplete="off">
                        <input type="hidden" name="_key" id="_key" value="<?php echo $_smarty_tpl->tpl_vars['firenet_encrypt']->value;?>
">
                        <input type="hidden" name="submitted" id="submitted" value="edit_admin">
                        <div class="form-group">
                            <label for="admusername">Username</label>
                            <input id="admusername" type="text" value="" class="form-control admusername" name="admusername" tabindex="1">
                        </div>
                        <div class="form-group">
                            <label for="admpassword">Password</label>
                            <input id="admpassword" type="text" value="" class="form-control admpassword" name="admpassword" tabindex="1">
                        </div>
                        <div class="form-group" id="admsettings">
                            <button type="button" class="btn btn-primary btn-confirm-adm" tabindex="4"> Confirm</button>
                            <button type="submit" class="btn btn-success btn-confirm-auth d-none" tabindex="4"> Authorize</button>
                            <button type="button" class="btn btn-confirm-cancel btn-danger d-none" tabindex="4">Cancel</button>
                        </div>
                    </form>
                </div>
            </div>
            <div class="card">
                <div class="card-header">
                    <h2 class="section-title">Clear Data</h2>
                </div>
                <div class="card-body">
                    <div class="alert alert-primary" role="alert">
                        <h6 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> Warning </h6>
                        <p>This will <code>delete</code> all the database records.</p>
                    </div>
                    <form class="clrsettings" accept-charset="UTF-8" autocomplete="off">
                        <input type="hidden" name="_key" id="_key" value="<?php echo $_smarty_tpl->tpl_vars['firenet_encrypt']->value;?>
">
                        <input type="hidden" name="submitted" id="submitted" value="reset_panel">
                        <div class="form-group">
                            <label for="usercounter">Users</label>
                            <input type="text" class="form-control usercounter" readonly="">
                        </div>
                        <div class="form-group">
                            <label for="resellercounter">Resellers</label>
                            <input type="text" class="form-control resellercounter" readonly="">
                        </div>
                        <div class="form-group" id="deletedata">
                            <button type="button" class="btn btn-primary btn-confirm-clr" tabindex="4"> Confirm</button>
                            <button type="submit" class="btn btn-success btn-confirm-auth d-none" tabindex="4"> Authorize</button>
                            <button type="button" class="btn btn-confirm-cancel btn-danger d-none" tabindex="4">Cancel</button>
                        </div>
                    </form>
                </div>
            </div>
            <div class="card">
                <div class="card-header">
                    <h2 class="section-title">Device Reset</h2>
                </div>
                <div class="card-body">
                    <div class="alert alert-primary" role="alert">
                        <h6 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> Warning </h6>
                        <p>This will <code>clear</code> all user's device information.</p>
                    </div>
                    <form class="clrdevice" accept-charset="UTF-8" autocomplete="off">
                        <input type="hidden" name="_key" id="_key" value="<?php echo $_smarty_tpl->tpl_vars['firenet_encrypt']->value;?>
">
                        <input type="hidden" name="submitted" id="submitted" value="reset_device">
                        <div class="form-group">
                            <label for="useractive">Active Users</label>
                            <input type="text" class="form-control useractive" readonly="">
                        </div>
                        <div class="form-group" id="deletedevice">
                            <button type="button" class="btn btn-primary btn-confirm-clrdevice" tabindex="4"> Confirm</button>
                            <button type="submit" class="btn btn-success btn-confirm-auth d-none" tabindex="4"> Authorize</button>
                            <button type="button" class="btn btn-confirm-cancel btn-danger d-none" tabindex="4">Cancel</button>
                        </div>
                    </form>
                </div>
            </div>
            </div>
            </div>
        </div>
    </section>
</div>

<div class="modal fade normal-modalize" role="dialog" aria-labelledby="smallmodal">
<div class="modal-dialog modal-md normal-modal-dialog" role="document">
<div class="modal-content normal-modal-content">
<div class="modal-header normal-modal-header">
<h5 class="modal-title normal-modal-title"></h5>
<button type="button" class="close" data-dismiss="modal">&times;</button>
</div>
<div class="modal-body normal-modal-body">
<div class="modal-error normal-modal-error"></div>
<div class="modal-html normal-modal-html"></div>
</div>
</div>
</div>
</div>

<div class="modal fade search-modalize" role="dialog" aria-labelledby="smallmodal">
<div class="modal-dialog modal-md search-modal-dialog" role="document">
<div class="modal-content search-modal-content">
<div class="modal-header search-modal-header">
<h5 class="modal-title search-modal-title"></h5>
<button type="button" class="close" data-dismiss="modal">&times;</button>
</div>
<div class="modal-body search-modal-body">
<div class="modal-error search-modal-error"></div>
<div class="modal-html search-modal-html"></div>
</div>
</div>
</div>
</div>

<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/jquery.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/popper.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/tooltip.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/bootstrap/js/bootstrap.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/nicescroll/jquery.nicescroll.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/moment.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/sweetalert2/sweetalert2.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/time.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/js/stisla.js"><?php echo '</script'; ?>
>

<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/chart.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/datatables/datatables.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/datatables/Select-1.2.4/js/dataTables.select.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/jquery-ui/jquery-ui.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/bootstrap/assets/jqueryform/jquery.form.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.9/summernote-bs4.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/select2.full.min.js"><?php echo '</script'; ?>
>

<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/js/clipboard.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/js/scripts.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/js/custom-select.js"><?php echo '</script'; ?>
>
<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:js/page/custom_js.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:js/page/notification_js.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:js/page/search_js.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:js/page/developer_js.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

</body>
</html><?php }
}
