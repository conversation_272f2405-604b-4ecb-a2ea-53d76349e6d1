<?php
/* Smarty version 3.1.29, created on 2025-07-13 22:39:32
  from "C:\xampp\htdocs\RajaGenWeb\templates\profile-settings.tpl" */

if ($_smarty_tpl->smarty->ext->_validateCompiled->decodeProperties($_smarty_tpl, array (
  'has_nocache_code' => false,
  'version' => '3.1.29',
  'unifunc' => 'content_68740b74b774e8_80539761',
  'file_dependency' => 
  array (
    'a29ad32fd875a5c162eb1b538f5326faadf0cf8a' => 
    array (
      0 => 'C:\\xampp\\htdocs\\RajaGenWeb\\templates\\profile-settings.tpl',
      1 => 1752434329,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:css/custom_css.tpl' => 1,
    'file:apps/topnav.tpl' => 1,
    'file:apps/sidenav.tpl' => 1,
    'file:js/page/custom_js.tpl' => 1,
    'file:js/page/notification_js.tpl' => 1,
    'file:js/page/profile_js.tpl' => 1,
    'file:js/page/search_js.tpl' => 1,
  ),
),false)) {
function content_68740b74b774e8_80539761 ($_smarty_tpl) {
?>
<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
<meta charset="UTF-8">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no" name="viewport">
<title><?php echo $_smarty_tpl->tpl_vars['site_name']->value;?>
 — <?php echo $_smarty_tpl->tpl_vars['site_description']->value;?>
</title>
<link rel="shortcut icon" href="<?php echo $_smarty_tpl->tpl_vars['site_logo']->value;?>
" type="image/x-icon">
<link rel="icon" href="<?php echo $_smarty_tpl->tpl_vars['site_logo']->value;?>
" type="image/x-icon">

<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" integrity="sha512-1ycn6IcaQQ40/MKBW2W4Rhis/DbILU74C1vSrLJxCq57o941Ym01SwNsOMqvEBFlcgUa6xLiPY/NS5R+E6ztJQ==" crossorigin="anonymous" referrerpolicy="no-referrer" />

<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/bootstrap-social/bootstrap-social.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/datatables/datatables.min.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/datatables/Select-1.2.4/css/select.bootstrap4.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.9/summernote-bs4.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/select2.min.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/sweetalert2/sweetalert2.min.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/css-<?php echo $_smarty_tpl->tpl_vars['site_theme']->value;?>
/style.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/css-<?php echo $_smarty_tpl->tpl_vars['site_theme']->value;?>
/components.css">
<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:css/custom_css.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

</head>
<body>

<div class="center" id="loading">
    <div class='building-blocks'>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
    </div>
</div>   
    
<div class="main-wrapper">
<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:apps/topnav.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:apps/sidenav.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>


<div class="main-content">
<section class="section">
<div class="section-header">
<h1>Profile Settings</h1>

<div class="section-header-breadcrumb">
    <div class="breadcrumb-item">Main</div>
    <div class="breadcrumb-item active">Profile</div>
</div>

</div>

<div class="section-body">
            <h2 class="section-title">Hi, <span class="profile-name">-</span>!</h2>
            <p>
              Change information about yourself on this page.
            </p>

            <div class="row mt-sm-4">
              <div class="col-12 col-md-12 col-lg-5">
                <div class="card profile-widget">
                  <div class="profile-widget-header">
                    <div class="profilepic profile-widget-picture" id="ppcture">
                      <?php echo $_smarty_tpl->tpl_vars['avatar3']->value;?>

                      <div class="profilepic__content" onclick="avatarchange()">
                        <span class="profilepic__icon"><i class="fas fa-camera"></i></span>
                        <span class="profilepic__text">Update Avatar</span>
                      </div>
                    </div>
                    <div class="profile-widget-items">
                      <div class="profile-widget-item">
                        <div class="profile-widget-item-label">Credit</div>
                        <div class="profile-widget-item-value profile-credit">-</div>
                      </div>
                      <div class="profile-widget-item">
                        <div class="profile-widget-item-label">User</div>
                        <div class="profile-widget-item-value profile-user">-</div>
                      </div>
                      <div class="profile-widget-item">
                        <div class="profile-widget-item-label">Reseller</div>
                        <div class="profile-widget-item-value profile-reseller">-</div>
                      </div>
                    </div>
                  </div>
                  <div class="profile-widget-description">
                    <div class="profile-widget-name"><span class="profile-name-2"></span> <div class="text-muted d-inline font-weight-normal"></div></div>
                    <span class="profile-bio-2"></span>
                  </div>
                  <div class="card-footer">
                    <btn type="button" class="btn btn-primary mr-1 mb-2" onclick="changepassword()">
                      <i class="fa fa-edit"></i> Password
                    </btn>
                    <btn type="button" class="btn btn-primary mr-1 mb-2 d-none" onclick="emailchange()">
                      <i class="fa fa-edit"></i> Email
                    </btn>
                  </div>
                </div>
              </div>
              <div class="col-12 col-md-12 col-lg-7">
                <div class="card">
                  <form class="profileupdate" accept-charset="UTF-8" autocomplete="off">
                    <div class="card-header">
                      <h4>Edit Profile</h4>
                    </div>
                    <div class="card-body">
                        <div class="section-error">
                            <div class="errors"></div>
                        </div>
                        <input type="hidden" name="_key" id="_key" value="<?php echo $_smarty_tpl->tpl_vars['firenet_encrypt']->value;?>
">
                        <input type="hidden" name="submitted" id="submitted" value="updateprofile">
                        <div class="row">
                          <div class="form-group col-md-6 col-12">
                            <label>First Name</label>
                            <input type="text" class="form-control data-firstname" name="firstname">
                          </div>
                          <div class="form-group col-md-6 col-12">
                            <label>Last Name</label>
                            <input type="text" class="form-control data-lastname" name="lastname">
                          </div>
                        </div>
                        <div class="row">
                          <div class="form-group col-md-6 col-12">
                            <label>Email</label>
                            <div class="input-group">
                                <input type="email" class="form-control data-email" name="email">
                                <div class="input-group-append">
                                  <div class="input-group-text verified-img">
                                    <i class="iyot"></i>
                                  </div>
                                </div>
                                <div class="" id="statemail">
                                    <span class="ver-msg"></span>
                                </div>
                            </div>
                          </div>
                          <div class="form-group col-md-6 col-12">
                            <label>Phone</label>
                            <input type="tel" class="form-control data-phone" name="phone">
                          </div>
                        </div>
                        <div class="row">
                          <div class="form-group col-12">
                            <label>Bio</label>
                            <textarea class="form-control summernote-simple data-bio" id="bio" name="bio"></textarea>
                          </div>
                        </div>
                        <div class="row">
                          <div class="form-group mb-0 col-12">
                            <div class="custom-control custom-checkbox">
                              <input type="checkbox" name="2fa" class="custom-control-input data-2fa" id="2fa">
                              <label class="custom-control-label" for="2fa">Two-factor authentication (2FA)</label>
                              <div class="text-muted form-text">
                                You will required to provide security code (OTP) everytime you login.<br> Make sure to provide working email address in your account.
                              </div>
                            </div>
                          </div>
                        </div>
                    </div>
                    <div class="card-footer">
                      <div class="form-group" id="profileupdate">
                        <button type="button" class="btn btn-primary btn-confirm-submit" tabindex="4"> Confirm</button>
                        <button type="submit" class="btn btn-success btn-confirm-auth d-none" tabindex="4"> Authorize</button>
                        <button type="button" class="btn btn-confirm-cancel btn-danger d-none" tabindex="4">Cancel</button>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
          
</section>
</div>
</div>
</div>

<div class="modal fade normal-modalize" role="dialog" aria-labelledby="smallmodal">
<div class="modal-dialog modal-md normal-modal-dialog" role="document">
<div class="modal-content normal-modal-content">
<div class="modal-header normal-modal-header">
<h5 class="modal-title normal-modal-title"></h5>
<button type="button" class="close" data-dismiss="modal">&times;</button>
</div>
<div class="modal-body normal-modal-body">
<div class="modal-error normal-modal-error"></div>
<div class="modal-html normal-modal-html"></div>
</div>
</div>
</div>
</div>

<div class="modal fade search-modalize" role="dialog" aria-labelledby="smallmodal">
<div class="modal-dialog modal-md search-modal-dialog" role="document">
<div class="modal-content search-modal-content">
<div class="modal-header search-modal-header">
<h5 class="modal-title search-modal-title"></h5>
<button type="button" class="close" data-dismiss="modal">&times;</button>
</div>
<div class="modal-body search-modal-body">
<div class="modal-error search-modal-error"></div>
<div class="modal-html search-modal-html"></div>
</div>
</div>
</div>
</div>

<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/jquery.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/popper.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/tooltip.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/bootstrap/js/bootstrap.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/nicescroll/jquery.nicescroll.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/moment.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/sweetalert2/sweetalert2.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/time.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/js/stisla.js"><?php echo '</script'; ?>
>

<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/chart.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/datatables/datatables.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/datatables/Select-1.2.4/js/dataTables.select.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/jquery-ui/jquery-ui.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/bootstrap/assets/jqueryform/jquery.form.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.9/summernote-bs4.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/select2.full.min.js"><?php echo '</script'; ?>
>

<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/js/clipboard.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/js/scripts.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/js/custom-select.js"><?php echo '</script'; ?>
>
<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:js/page/custom_js.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:js/page/notification_js.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:js/page/profile_js.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:js/page/search_js.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

</body>
</html><?php }
}
