<?php
/* Smarty version 3.1.29, created on 2025-07-13 22:45:09
  from "C:\xampp\htdocs\RajaGenWeb\templates\js\page\custom_js.tpl" */

if ($_smarty_tpl->smarty->ext->_validateCompiled->decodeProperties($_smarty_tpl, array (
  'has_nocache_code' => false,
  'version' => '3.1.29',
  'unifunc' => 'content_68740cc597f801_17177349',
  'file_dependency' => 
  array (
    '6f216589e48a7981090d12afbafa9e9d2a8b732f' => 
    array (
      0 => 'C:\\xampp\\htdocs\\RajaGenWeb\\templates\\js\\page\\custom_js.tpl',
      1 => 1752435841,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_68740cc597f801_17177349 ($_smarty_tpl) {
echo '<script'; ?>
>
function normal_modalize(title, body)
	{
	    $(".normal-modalize").modal({
            backdrop: 'static',
            keyboard: false  // to prevent closing with Esc button (if you want this too)
        })
        $(".normal-modalize").modal('show');
		$(".normal-modal-title").text(title);
		$(".normal-modal-error").html('');
		$(".normal-modal-html").html(body);
	}

$(function() {
	var clippy = new ClipboardJS('.btn-copy');
	clippy.on('success', function(e) {
	    $('.normal-modalize').modal('hide');
		Swal.fire({
            title: "Details copied successfuly!",
            icon: "success",
            allowOutsideClick: false,
            allowEscapeKey: false,
            confirmButtonText: "Confirm",
            didOpen: function () {
                Swal.getConfirmButton().blur()
            },
            customClass: {
                confirmButton: 'swal2-confirm btn btn-primary swal2-styled'
            }
        }); e.clearSelection();
	}); var token = $("#app").data("token");
	
	var kiffy = new ClipboardJS('.btn-xcopy');
	$.fn.modal.Constructor.prototype._enforceFocus = function() {};
	kiffy.on('success', function(e) {
	    $('.normal-modalize').modal('hide');
		Swal.fire({
            title: "Link copied successfuly!",
            icon: "success",
            allowOutsideClick: false,
            allowEscapeKey: false,
            confirmButtonText: "Confirm",
            didOpen: function () {
                Swal.getConfirmButton().blur()
            },
            customClass: {
                confirmButton: 'swal2-confirm btn btn-primary swal2-styled'
            }
        }); e.clearSelection();
	}); var token = $("#xxapp").data("token");
	
	$(".btn-logout").click(function() {
		Swal.fire({
          text: "Are you sure you want to logout?",
          allowOutsideClick: false,
          allowEscapeKey: false,
          imageUrl: "<?php echo $_smarty_tpl->tpl_vars['site_logo']->value;?>
",
          imageWidth: 150,
          imageHeight: 150,
          imageAlt: "Custom image",
          showCancelButton: true,
          confirmButtonText: `Confirm`,
          didOpen: function () {
            Swal.getConfirmButton().blur()
          },
          customClass: {
                confirmButton: 'swal2-confirm btn btn-primary swal2-styled',
                cancelButton: 'swal2-confirm btn btn-danger swal2-styled'
            }
        }).then((doLogout) => {
			if (doLogout.isConfirmed) {
				location.replace("<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
logout");
			}
		});
	})
	
    $('.btn-genuine').click(function () {
        normal_modalize('Made with \u2764️ by Firenet','Thank you for choosing my vpn panel and for supporting me.')
    })
})

        $(document).on('click', '.upload-field', function(){
            var file = $(this).parent().parent().find('.input-file');
            file.trigger('click');
        });

        $(document).on('change', '.input-file', function(){
            $(this).parent().find('.form-control').val($(this).val().replace(/C:\\fakepath\\/i, ''));
        });
        
        $(document).on('click', '.upload-field2', function(){
            var file2 = $(this).parent().parent().parent().find('.input-file2');
            file2.trigger('click');
        });

        $(document).on('change', '.input-file2', function(){
            $(this).parent().find('.form-control').val($(this).val().replace(/C:\\fakepath\\/i, ''));
        });
        
$(window).on('load', function() {
   $("#loading").hide();
});

/**
* Utility function to calculate the current theme setting.
* Look for a local storage value.
* Fall back to system setting.
* Fall back to light mode.
*/
function calculateSettingAsThemeString({ localStorageTheme, systemSettingDark }) {
  if (localStorageTheme !== null) {
    return localStorageTheme;
  }

  if (systemSettingDark.matches) {
    return "dark";
  }

  return "light";
}

/**
* Utility function to update the button text and aria-label.
*/
function updateButton({ buttonEl, isDark }) {
  const newCta = isDark ? "fa fa-sun" : "fa fa-moon";
  // use an aria-label if you are omitting text on the button
  // and using a sun/moon icon, for example
  $('#xtoggle').attr("class",newCta);
}

/**
* Utility function to update the theme setting on the html tag
*/
function updateThemeOnHtmlEl({ theme }) {
  document.querySelector("html").setAttribute("data-theme", theme);
}


/**
* On page load:
*/

/**
* 1. Grab what we need from the DOM and system settings on page load
*/
const button = document.querySelector("[data-theme-toggle]");
const localStorageTheme = localStorage.getItem("theme");
const systemSettingDark = window.matchMedia("(prefers-color-scheme: dark)");

/**
* 2. Work out the current site settings
*/
let currentThemeSetting = calculateSettingAsThemeString({ localStorageTheme, systemSettingDark });

/**
* 3. Update the theme setting and button text accoridng to current settings
*/
updateButton({ buttonEl: button, isDark: currentThemeSetting === "dark" });
updateThemeOnHtmlEl({ theme: currentThemeSetting });

/**
* 4. Add an event listener to toggle the theme
*/
button.addEventListener("click", (event) => {
  const newTheme = currentThemeSetting === "dark" ? "light" : "dark";

  localStorage.setItem("theme", newTheme);
  updateButton({ buttonEl: button, isDark: newTheme === "dark" });
  updateThemeOnHtmlEl({ theme: newTheme });

  currentThemeSetting = newTheme;
}); 
<?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="https://challenges.cloudflare.com/turnstile/v0/api.js?onload=onloadTurnstileCallback" defer><?php echo '</script'; ?>
><?php }
}
